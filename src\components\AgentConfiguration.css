.agent-config-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.agent-config-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 800px;
  max-height: 90vh;
  width: 90%;
  overflow: hidden;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.config-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.config-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.config-content {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 2rem;
}

.config-section h3 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1.2rem;
  font-weight: 600;
}

.mode-selector {
  display: flex;
  gap: 1rem;
}

.mode-option {
  flex: 1;
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.mode-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.mode-option.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.mode-option input[type="radio"] {
  margin: 0;
}

.mode-info {
  flex: 1;
}

.mode-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.mode-description {
  font-size: 0.9rem;
  color: #6b7280;
}

.provider-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.provider-option {
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.provider-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.provider-option.selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.provider-option input[type="radio"] {
  margin-top: 0.25rem;
}

.provider-name {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.provider-description {
  font-size: 0.85rem;
  color: #6b7280;
  line-height: 1.4;
}

.api-key-input {
  position: relative;
}

.api-key-input input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.api-key-input input:focus {
  outline: none;
  border-color: #3b82f6;
}

.api-key-input input.valid {
  border-color: #10b981;
}

.api-key-input input.invalid {
  border-color: #ef4444;
}

.api-key-status {
  margin-top: 0.5rem;
  font-size: 0.9rem;
}

.status-valid {
  color: #10b981;
  font-weight: 500;
}

.status-invalid {
  color: #ef4444;
  font-weight: 500;
}

.model-select {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
}

.model-select:focus {
  outline: none;
  border-color: #3b82f6;
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.tool-option {
  padding: 1rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.tool-option:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.tool-option.selected {
  border-color: #10b981;
  background: #ecfdf5;
}

.tool-option input[type="checkbox"] {
  margin-top: 0.25rem;
}

.tool-name {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.25rem;
}

.tool-category {
  font-size: 0.75rem;
  color: #3b82f6;
  font-weight: 500;
  margin-bottom: 0.25rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tool-description {
  font-size: 0.85rem;
  color: #6b7280;
  line-height: 1.4;
}

.advanced-settings {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.setting-row {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.setting-row label {
  font-weight: 500;
  color: #374151;
}

.setting-row input[type="range"] {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
  cursor: pointer;
}

.setting-row input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
}

.setting-row input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: none;
}

.memory-settings {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.setting-description {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

.config-footer {
  background: #f9fafb;
  padding: 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  border-top: 1px solid #e5e7eb;
}

.cancel-button,
.save-button {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button {
  background: #f3f4f6;
  color: #374151;
}

.cancel-button:hover {
  background: #e5e7eb;
}

.save-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.save-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Scrollbar styling */
.config-content::-webkit-scrollbar {
  width: 6px;
}

.config-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.config-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.config-content::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
