const express = require('express');
const cors = require('cors');
const https = require('https');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Microchip AI Proxy Server is running' });
});

// Proxy endpoint for Microchip AI API
app.post('/api/chat', async (req, res) => {
  try {
    const { questions, answers, category, logQnA, client, apiKey } = req.body;

    // Validate required fields
    if (!questions || !Array.isArray(questions) || questions.length === 0) {
      return res.status(400).json({
        error: 'Questions array is required and must not be empty'
      });
    }

    if (!apiKey) {
      return res.status(400).json({
        error: 'API key is required'
      });
    }

    // Prepare the request data
    const postData = JSON.stringify({
      questions,
      answers: answers || [],
      category: category || 101,
      logQnA: logQnA !== undefined ? logQnA : true,
      client: client || "react-chatbot-proxy"
    });

    // Configure the request options
    const options = {
      hostname: 'ai-apps.microchip.com',
      port: 443,
      path: '/CodeGPTAPI/api/Chat/CodeCompletion',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'api-key': apiKey,
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'Microchip-Chatbot-Proxy/1.0'
      },
      timeout: 30000 // 30 second timeout
    };

    // Make the request to Microchip API
    const apiRequest = https.request(options, (apiResponse) => {
      let data = '';

      apiResponse.on('data', (chunk) => {
        data += chunk;
      });

      apiResponse.on('end', () => {
        try {
          // Set the response status code from the API
          res.status(apiResponse.statusCode);

          if (apiResponse.statusCode === 200) {
            let botResponse = '';
            
            // Try to parse as JSON first
            try {
              const response = JSON.parse(data);
              
              // Handle different possible JSON response formats
              if (response.response) {
                botResponse = response.response;
              } else if (response.answer) {
                botResponse = response.answer;
              } else if (response.content) {
                botResponse = response.content;
              } else if (response.message) {
                botResponse = response.message;
              } else if (response.result) {
                botResponse = response.result;
              } else if (typeof response === 'string') {
                botResponse = response;
              } else if (Array.isArray(response) && response.length > 0) {
                botResponse = response[0].response || response[0].answer || JSON.stringify(response[0]);
              } else {
                botResponse = JSON.stringify(response);
              }
              
              res.json({ 
                success: true, 
                message: botResponse,
                rawResponse: response 
              });
            } catch (jsonError) {
              // If JSON parsing fails, treat the response as plain text
              botResponse = data.trim();
              
              if (botResponse.length === 0) {
                botResponse = 'I received an empty response from the API.';
              }
              
              res.json({ 
                success: true, 
                message: botResponse 
              });
            }
          } else {
            // Handle error responses
            let errorMessage = `HTTP ${apiResponse.statusCode}: ${apiResponse.statusMessage}`;
            
            if (apiResponse.statusCode === 401) {
              errorMessage = 'Invalid API key or unauthorized access';
            } else if (apiResponse.statusCode === 429) {
              errorMessage = 'Rate limit exceeded. Please wait and try again';
            } else if (apiResponse.statusCode >= 500) {
              errorMessage = 'Server error. Please try again later';
            }
            
            res.json({
              success: false,
              error: errorMessage,
              statusCode: apiResponse.statusCode,
              details: data
            });
          }
        } catch (error) {
          console.error('Error processing API response:', error);
          res.status(500).json({
            success: false,
            error: 'Failed to process API response'
          });
        }
      });
    });

    apiRequest.on('error', (error) => {
      console.error('API request error:', error);
      res.status(500).json({
        success: false,
        error: `Network error: ${error.message}`
      });
    });

    apiRequest.on('timeout', () => {
      apiRequest.destroy();
      res.status(408).json({
        success: false,
        error: 'Request timeout - API took too long to respond'
      });
    });

    // Write data to request body
    apiRequest.write(postData);
    apiRequest.end();

  } catch (error) {
    console.error('Server error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// Start the server
const server = app.listen(PORT, () => {
  console.log(`🚀 Microchip AI Proxy Server running on port ${PORT}`);
  console.log(`📡 Health check: http://localhost:${PORT}/health`);
  console.log(`🤖 Chat endpoint: http://localhost:${PORT}/api/chat`);
});

// Keep the process alive
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

module.exports = app;
