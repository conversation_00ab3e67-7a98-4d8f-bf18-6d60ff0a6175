const express = require('express');
const cors = require('cors');
const https = require('https');
const { spawn } = require('child_process');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json());

// In-memory storage for agent configurations and conversations
const agentConfigurations = new Map();
const conversations = new Map();

// Simulate agent response (placeholder for actual LangChain integration)
async function simulateAgentResponse(message, config, conversationId) {
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 500));

  const response = {
    message: '',
    toolCalls: [],
    metadata: {
      provider: config.provider,
      model: config.model || 'default',
      timestamp: new Date()
    }
  };

  // Simple response generation based on provider
  switch (config.provider) {
    case 'openai':
      response.message = `OpenAI GPT response to: "${message}". This is a simulated response that would normally come from the OpenAI API.`;
      break;
    case 'anthropic':
      response.message = `Claude response to: "${message}". This is a simulated response that would normally come from the Anthropic API.`;
      break;
    case 'google':
      response.message = `Gemini response to: "${message}". This is a simulated response that would normally come from the Google AI API.`;
      break;
    case 'microchip':
    default:
      response.message = `Microchip AI response to: "${message}". This is a simulated response that would normally come from the Microchip AI API.`;
      break;
  }

  // Simulate tool usage if enabled
  if (config.enableTools) {
    if (message.toLowerCase().includes('calculate') || message.toLowerCase().includes('math')) {
      response.toolCalls.push({
        id: `tool_${Date.now()}`,
        name: 'calculator',
        input: { expression: 'simulated calculation' },
        output: 'Calculation result: 42'
      });
    }

    if (message.toLowerCase().includes('search') || message.toLowerCase().includes('find')) {
      response.toolCalls.push({
        id: `tool_${Date.now() + 1}`,
        name: 'web_search',
        input: { query: message },
        output: 'Search results: [Simulated search results]'
      });
    }
  }

  return response;
}

// Utility function to validate API keys
function validateApiKey(provider, apiKey) {
  if (!apiKey || typeof apiKey !== 'string' || apiKey.trim().length === 0) {
    return false;
  }

  // Basic validation based on provider
  switch (provider) {
    case 'openai':
      return apiKey.startsWith('sk-');
    case 'anthropic':
      return apiKey.startsWith('sk-ant-');
    case 'google':
      return apiKey.length > 10; // Basic length check
    case 'microchip':
      return apiKey.length > 5; // Basic length check
    default:
      return false;
  }
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Microchip AI Proxy Server is running' });
});

// Agent configuration endpoint
app.post('/api/agent/configure', (req, res) => {
  try {
    const { sessionId, config } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Session ID is required'
      });
    }

    if (!config || !config.provider) {
      return res.status(400).json({
        success: false,
        error: 'Agent configuration with provider is required'
      });
    }

    // Store agent configuration
    agentConfigurations.set(sessionId, {
      ...config,
      timestamp: new Date()
    });

    res.json({
      success: true,
      message: 'Agent configuration saved',
      sessionId: sessionId
    });

  } catch (error) {
    console.error('Agent configuration error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to configure agent'
    });
  }
});

// Get agent configuration
app.get('/api/agent/config/:sessionId', (req, res) => {
  try {
    const { sessionId } = req.params;
    const config = agentConfigurations.get(sessionId);

    if (!config) {
      return res.status(404).json({
        success: false,
        error: 'Agent configuration not found'
      });
    }

    res.json({
      success: true,
      config: config
    });

  } catch (error) {
    console.error('Get agent config error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get agent configuration'
    });
  }
});

// Agent chat endpoint
app.post('/api/agent/chat', async (req, res) => {
  try {
    const { sessionId, message, conversationId } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Session ID is required'
      });
    }

    if (!message || typeof message !== 'string' || message.trim().length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Message is required and must be a non-empty string'
      });
    }

    const config = agentConfigurations.get(sessionId);
    if (!config) {
      return res.status(400).json({
        success: false,
        error: 'Agent not configured for this session'
      });
    }

    // For now, simulate agent response
    // In a real implementation, this would use the LangChain agent
    const response = await simulateAgentResponse(message, config, conversationId);

    // Store conversation
    const convId = conversationId || `conv_${Date.now()}`;
    if (!conversations.has(convId)) {
      conversations.set(convId, []);
    }

    const conversation = conversations.get(convId);
    conversation.push({
      id: `msg_${Date.now()}`,
      role: 'user',
      content: message,
      timestamp: new Date()
    });

    conversation.push({
      id: `msg_${Date.now() + 1}`,
      role: 'assistant',
      content: response.message,
      timestamp: new Date(),
      toolCalls: response.toolCalls
    });

    res.json({
      success: true,
      message: response.message,
      conversationId: convId,
      toolCalls: response.toolCalls,
      metadata: response.metadata
    });

  } catch (error) {
    console.error('Agent chat error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process agent chat'
    });
  }
});

// Get conversation history
app.get('/api/agent/conversation/:conversationId', (req, res) => {
  try {
    const { conversationId } = req.params;
    const conversation = conversations.get(conversationId) || [];

    res.json({
      success: true,
      conversation: conversation
    });

  } catch (error) {
    console.error('Get conversation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get conversation'
    });
  }
});

// Clear conversation
app.delete('/api/agent/conversation/:conversationId', (req, res) => {
  try {
    const { conversationId } = req.params;
    conversations.delete(conversationId);

    res.json({
      success: true,
      message: 'Conversation cleared'
    });

  } catch (error) {
    console.error('Clear conversation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear conversation'
    });
  }
});

// Get available tools
app.get('/api/agent/tools', (req, res) => {
  try {
    const tools = [
      {
        name: 'calculator',
        description: 'Perform mathematical calculations',
        enabled: true
      },
      {
        name: 'web_search',
        description: 'Search the web for information',
        enabled: true
      },
      {
        name: 'code_analysis',
        description: 'Analyze code snippets',
        enabled: true
      },
      {
        name: 'microchip_docs',
        description: 'Search Microchip documentation',
        enabled: true
      }
    ];

    res.json({
      success: true,
      tools: tools
    });

  } catch (error) {
    console.error('Get tools error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get available tools'
    });
  }
});

// Execute tool
app.post('/api/agent/tool/execute', async (req, res) => {
  try {
    const { toolName, input, sessionId } = req.body;

    if (!toolName || !input) {
      return res.status(400).json({
        success: false,
        error: 'Tool name and input are required'
      });
    }

    // Simulate tool execution
    let result = '';
    switch (toolName) {
      case 'calculator':
        result = `Calculation result: ${input.expression || 'No expression provided'}`;
        break;
      case 'web_search':
        result = `Search results for "${input.query || 'No query provided'}": [Simulated results]`;
        break;
      case 'code_analysis':
        result = `Code analysis for: ${input.code ? 'Code provided' : 'No code provided'}`;
        break;
      case 'microchip_docs':
        result = `Microchip documentation search for "${input.query || 'No query provided'}": [Simulated docs]`;
        break;
      default:
        result = `Unknown tool: ${toolName}`;
    }

    res.json({
      success: true,
      toolName: toolName,
      input: input,
      output: result,
      executionTime: Math.random() * 1000 + 500 // Simulate execution time
    });

  } catch (error) {
    console.error('Tool execution error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to execute tool'
    });
  }
});

// Proxy endpoint for Microchip AI API
app.post('/api/chat', async (req, res) => {
  try {
    const { questions, answers, category, logQnA, client, apiKey } = req.body;

    // Validate required fields
    if (!questions || !Array.isArray(questions) || questions.length === 0) {
      return res.status(400).json({
        error: 'Questions array is required and must not be empty'
      });
    }

    if (!apiKey) {
      return res.status(400).json({
        error: 'API key is required'
      });
    }

    // Prepare the request data
    const postData = JSON.stringify({
      questions,
      answers: answers || [],
      category: category || 101,
      logQnA: logQnA !== undefined ? logQnA : true,
      client: client || "react-chatbot-proxy"
    });

    // Configure the request options
    const options = {
      hostname: 'ai-apps.microchip.com',
      port: 443,
      path: '/CodeGPTAPI/api/Chat/CodeCompletion',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json; charset=utf-8',
        'api-key': apiKey,
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'Microchip-Chatbot-Proxy/1.0'
      },
      timeout: 30000 // 30 second timeout
    };

    // Make the request to Microchip API
    const apiRequest = https.request(options, (apiResponse) => {
      let data = '';

      apiResponse.on('data', (chunk) => {
        data += chunk;
      });

      apiResponse.on('end', () => {
        try {
          // Set the response status code from the API
          res.status(apiResponse.statusCode);

          if (apiResponse.statusCode === 200) {
            let botResponse = '';
            
            // Try to parse as JSON first
            try {
              const response = JSON.parse(data);
              
              // Handle different possible JSON response formats
              if (response.response) {
                botResponse = response.response;
              } else if (response.answer) {
                botResponse = response.answer;
              } else if (response.content) {
                botResponse = response.content;
              } else if (response.message) {
                botResponse = response.message;
              } else if (response.result) {
                botResponse = response.result;
              } else if (typeof response === 'string') {
                botResponse = response;
              } else if (Array.isArray(response) && response.length > 0) {
                botResponse = response[0].response || response[0].answer || JSON.stringify(response[0]);
              } else {
                botResponse = JSON.stringify(response);
              }
              
              res.json({ 
                success: true, 
                message: botResponse,
                rawResponse: response 
              });
            } catch (jsonError) {
              // If JSON parsing fails, treat the response as plain text
              botResponse = data.trim();
              
              if (botResponse.length === 0) {
                botResponse = 'I received an empty response from the API.';
              }
              
              res.json({ 
                success: true, 
                message: botResponse 
              });
            }
          } else {
            // Handle error responses
            let errorMessage = `HTTP ${apiResponse.statusCode}: ${apiResponse.statusMessage}`;
            
            if (apiResponse.statusCode === 401) {
              errorMessage = 'Invalid API key or unauthorized access';
            } else if (apiResponse.statusCode === 429) {
              errorMessage = 'Rate limit exceeded. Please wait and try again';
            } else if (apiResponse.statusCode >= 500) {
              errorMessage = 'Server error. Please try again later';
            }
            
            res.json({
              success: false,
              error: errorMessage,
              statusCode: apiResponse.statusCode,
              details: data
            });
          }
        } catch (error) {
          console.error('Error processing API response:', error);
          res.status(500).json({
            success: false,
            error: 'Failed to process API response'
          });
        }
      });
    });

    apiRequest.on('error', (error) => {
      console.error('API request error:', error);
      res.status(500).json({
        success: false,
        error: `Network error: ${error.message}`
      });
    });

    apiRequest.on('timeout', () => {
      apiRequest.destroy();
      res.status(408).json({
        success: false,
        error: 'Request timeout - API took too long to respond'
      });
    });

    // Write data to request body
    apiRequest.write(postData);
    apiRequest.end();

  } catch (error) {
    console.error('Server error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal server error'
  });
});

// Start the server
const server = app.listen(PORT, () => {
  console.log(`🚀 Microchip AI Proxy Server running on port ${PORT}`);
  console.log(`📡 Health check: http://localhost:${PORT}/health`);
  console.log(`🤖 Chat endpoint: http://localhost:${PORT}/api/chat`);
});

// Keep the process alive
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

module.exports = app;
