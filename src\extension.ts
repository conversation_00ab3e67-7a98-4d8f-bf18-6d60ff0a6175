import * as vscode from 'vscode';
import { ChatbotWebviewProvider } from './webview/ChatbotWebviewProvider';
import { ServerManager } from './services/ServerManager';

export function activate(context: vscode.ExtensionContext) {
    console.log('Microchip AI Chatbot extension is now active!');

    // Create the webview provider
    const chatbotProvider = new ChatbotWebviewProvider(context.extensionUri, context);

    // Register the webview provider
    context.subscriptions.push(
        vscode.window.registerWebviewViewProvider(
            'microchipAIChatbot',
            chatbotProvider,
            {
                webviewOptions: {
                    retainContextWhenHidden: true
                }
            }
        )
    );

    // Register commands
    const openChatCommand = vscode.commands.registerCommand('microchipAIChatbot.openChat', () => {
        chatbotProvider.openChatPanel();
    });

    const resetApiKeyCommand = vscode.commands.registerCommand('microchipAIChatbot.resetApiKey', () => {
        chatbotProvider.resetApiKey();
    });

    context.subscriptions.push(openChatCommand, resetApiKeyCommand);

    // Automatically open the chat panel on activation
    setTimeout(() => {
        chatbotProvider.openChatPanel();
    }, 1000); // Small delay to ensure VS Code is fully loaded

    // Show welcome message on first activation
    const hasShownWelcome = context.globalState.get('hasShownWelcome', false);
    if (!hasShownWelcome) {
        vscode.window.showInformationMessage(
            'Microchip AI Chatbot is now available! The chat panel has been opened automatically.',
            'Got it'
        );
        context.globalState.update('hasShownWelcome', true);
    }
}

export async function deactivate() {
    console.log('Microchip AI Chatbot extension is now deactivated.');

    // Stop the server when extension is deactivated
    const serverManager = ServerManager.getInstance();
    await serverManager.stopServer();
    serverManager.dispose();
}
