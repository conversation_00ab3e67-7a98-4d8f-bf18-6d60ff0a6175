import { useState } from 'react'
import { ApiKeyInput } from './components/ApiKeyInput'
import { Chatbot } from './components/Chatbot'
import { AgentChatbot } from './components/AgentChatbot'
import './App.css'

function App() {
  const [isApiKeySet, setIsApiKeySet] = useState(false)
  const [apiKey, setApiKey] = useState('')
  const [useEnhancedMode, setUseEnhancedMode] = useState(true)

  const handleApiKeySet = (isValid: boolean, key?: string) => {
    setIsApiKeySet(isValid)
    if (key) {
      setApiKey(key)
    }
  }

  const handleDisconnect = () => {
    setIsApiKeySet(false)
    setApiKey('')
  }

  const toggleMode = () => {
    setUseEnhancedMode(!useEnhancedMode)
  }

  return (
    <div className="app">
      {!isApiKeySet ? (
        <ApiKeyInput onApiKeySet={handleApiKeySet} />
      ) : (
        <div>
          <div className="mode-toggle">
            <button onClick={toggleMode} className="toggle-button">
              {useEnhancedMode ? '🧠 Enhanced Mode' : '💬 Simple Mode'}
            </button>
          </div>
          {useEnhancedMode ? (
            <AgentChatbot apiKey={apiKey} onDisconnect={handleDisconnect} />
          ) : (
            <Chatbot onDisconnect={handleDisconnect} />
          )}
        </div>
      )}
    </div>
  )
}

export default App
