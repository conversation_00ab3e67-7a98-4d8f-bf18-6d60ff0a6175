import { spawn, ChildProcess } from 'child_process';
import * as path from 'path';
import * as vscode from 'vscode';

export class ServerManager {
    private static instance: ServerManager;
    private serverProcess: ChildProcess | null = null;
    private isStarting = false;
    private isShuttingDown = false;
    private readonly serverPort = 3001;
    private readonly maxStartupTime = 10000; // 10 seconds
    private startupPromise: Promise<boolean> | null = null;

    private constructor() {}

    public static getInstance(): ServerManager {
        if (!ServerManager.instance) {
            ServerManager.instance = new ServerManager();
        }
        return ServerManager.instance;
    }

    public async startServer(extensionPath: string): Promise<boolean> {
        if (this.serverProcess && !this.serverProcess.killed) {
            console.log('Server is already running');
            return true;
        }

        if (this.isStarting && this.startupPromise) {
            console.log('Server startup already in progress, waiting...');
            return this.startupPromise;
        }

        this.isStarting = true;
        this.startupPromise = this._startServerInternal(extensionPath);
        
        try {
            const result = await this.startupPromise;
            return result;
        } finally {
            this.isStarting = false;
            this.startupPromise = null;
        }
    }

    private async _startServerInternal(extensionPath: string): Promise<boolean> {
        return new Promise((resolve, reject) => {
            try {
                const serverScript = path.join(extensionPath, 'server', 'index.cjs');
                console.log(`Starting server from: ${serverScript}`);

                // Spawn the server process
                this.serverProcess = spawn('node', [serverScript], {
                    cwd: extensionPath,
                    stdio: ['pipe', 'pipe', 'pipe'],
                    env: { ...process.env, PORT: this.serverPort.toString() }
                });

                let serverStarted = false;
                const startupTimeout = setTimeout(() => {
                    if (!serverStarted) {
                        console.error('Server startup timeout');
                        this.killServer();
                        resolve(false);
                    }
                }, this.maxStartupTime);

                // Handle server output
                this.serverProcess.stdout?.on('data', (data) => {
                    const output = data.toString();
                    console.log(`Server stdout: ${output}`);
                    
                    // Check if server started successfully
                    if (output.includes('Microchip AI Proxy Server running') && !serverStarted) {
                        serverStarted = true;
                        clearTimeout(startupTimeout);
                        console.log('Server started successfully');
                        resolve(true);
                    }
                });

                this.serverProcess.stderr?.on('data', (data) => {
                    const error = data.toString();
                    console.error(`Server stderr: ${error}`);
                    
                    // Check for port already in use error
                    if (error.includes('EADDRINUSE') && !serverStarted) {
                        console.log('Port already in use, assuming server is already running');
                        serverStarted = true;
                        clearTimeout(startupTimeout);
                        resolve(true);
                    }
                });

                this.serverProcess.on('error', (error) => {
                    console.error('Failed to start server:', error);
                    clearTimeout(startupTimeout);
                    if (!serverStarted) {
                        resolve(false);
                    }
                });

                this.serverProcess.on('exit', (code, signal) => {
                    console.log(`Server process exited with code ${code} and signal ${signal}`);
                    this.serverProcess = null;
                    clearTimeout(startupTimeout);
                    if (!serverStarted) {
                        resolve(false);
                    }
                });

            } catch (error) {
                console.error('Error starting server:', error);
                resolve(false);
            }
        });
    }

    public async stopServer(): Promise<void> {
        if (this.isShuttingDown) {
            console.log('Server shutdown already in progress');
            return;
        }

        if (!this.serverProcess || this.serverProcess.killed) {
            console.log('Server is not running');
            return;
        }

        this.isShuttingDown = true;

        return new Promise((resolve) => {
            const shutdownTimeout = setTimeout(() => {
                console.log('Server shutdown timeout, force killing');
                this.killServer();
                resolve();
            }, 5000); // 5 second timeout for graceful shutdown

            this.serverProcess!.on('exit', () => {
                clearTimeout(shutdownTimeout);
                console.log('Server stopped gracefully');
                this.serverProcess = null;
                this.isShuttingDown = false;
                resolve();
            });

            // Send SIGTERM for graceful shutdown
            console.log('Stopping server gracefully...');
            this.serverProcess!.kill('SIGTERM');
        });
    }

    private killServer(): void {
        if (this.serverProcess && !this.serverProcess.killed) {
            console.log('Force killing server process');
            this.serverProcess.kill('SIGKILL');
            this.serverProcess = null;
        }
        this.isShuttingDown = false;
    }

    public isServerRunning(): boolean {
        return this.serverProcess !== null && !this.serverProcess.killed;
    }

    public async checkServerHealth(): Promise<boolean> {
        try {
            const response = await fetch(`http://localhost:${this.serverPort}/health`);
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    public getServerPort(): number {
        return this.serverPort;
    }

    public dispose(): void {
        if (this.serverProcess && !this.serverProcess.killed) {
            this.killServer();
        }
    }
}
