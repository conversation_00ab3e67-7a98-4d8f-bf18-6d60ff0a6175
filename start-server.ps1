# PowerShell script to start the Microchip AI Backend Server

Write-Host "Starting Microchip AI Backend Server..." -ForegroundColor Green
Write-Host ""
Write-Host "This window must stay open for the VS Code extension to work." -ForegroundColor Yellow
Write-Host "You can minimize this window, but do not close it." -ForegroundColor Yellow
Write-Host ""
Write-Host "To stop the server, press Ctrl+C in this window." -ForegroundColor Cyan
Write-Host ""

try {
    & node server/index.cjs
} catch {
    Write-Host "Error starting server: $_" -ForegroundColor Red
}

Write-Host ""
Write-Host "Server has stopped. Press any key to close this window." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
