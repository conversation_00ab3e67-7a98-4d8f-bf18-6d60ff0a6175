import { Chat<PERSON>penAI } from '@langchain/openai';
import { ChatAnthropic } from '@langchain/anthropic';
import { ChatGoogleGenerativeAI } from '@langchain/google-genai';
import { AgentExecutor, createReactAgent } from 'langchain/agents';
import { pull } from 'langchain/hub';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { Tool } from '@langchain/core/tools';
import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { BufferMemory } from 'langchain/memory';
import { ConversationChain } from 'langchain/chains';
import { v4 as uuidv4 } from 'uuid';

export interface AgentConfig {
    provider: 'openai' | 'anthropic' | 'google' | 'microchip';
    apiKey: string;
    model?: string;
    temperature?: number;
    maxTokens?: number;
    enableTools?: boolean;
    enableMemory?: boolean;
    systemPrompt?: string;
}

export interface ConversationMessage {
    id: string;
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
    toolCalls?: ToolCall[];
    metadata?: Record<string, any>;
}

export interface ToolCall {
    id: string;
    name: string;
    input: Record<string, any>;
    output?: any;
    error?: string;
}

export interface AgentResponse {
    message: string;
    toolCalls?: ToolCall[];
    metadata?: Record<string, any>;
    conversationId: string;
}

export class LangChainAgentService {
    private llm: ChatOpenAI | ChatAnthropic | ChatGoogleGenerativeAI | null = null;
    private agent: AgentExecutor | null = null;
    private memory: BufferMemory;
    private tools: Tool[] = [];
    private config: AgentConfig | null = null;
    private conversations: Map<string, ConversationMessage[]> = new Map();
    private currentConversationId: string = '';

    constructor() {
        this.memory = new BufferMemory({
            returnMessages: true,
            memoryKey: 'chat_history',
            inputKey: 'input',
            outputKey: 'output'
        });
        this.currentConversationId = uuidv4();
    }

    public async initialize(config: AgentConfig): Promise<boolean> {
        try {
            this.config = config;
            
            // Initialize the appropriate LLM based on provider
            await this.initializeLLM(config);
            
            // Initialize tools if enabled
            if (config.enableTools) {
                await this.initializeTools();
            }
            
            // Initialize agent if tools are available
            if (this.tools.length > 0) {
                await this.initializeAgent();
            }
            
            console.log('LangChain Agent Service initialized successfully');
            return true;
        } catch (error) {
            console.error('Failed to initialize LangChain Agent Service:', error);
            return false;
        }
    }

    private async initializeLLM(config: AgentConfig): Promise<void> {
        const commonConfig = {
            temperature: config.temperature || 0.7,
            maxTokens: config.maxTokens || 2000,
        };

        switch (config.provider) {
            case 'openai':
                this.llm = new ChatOpenAI({
                    openAIApiKey: config.apiKey,
                    modelName: config.model || 'gpt-3.5-turbo',
                    ...commonConfig
                });
                break;
            
            case 'anthropic':
                this.llm = new ChatAnthropic({
                    anthropicApiKey: config.apiKey,
                    modelName: config.model || 'claude-3-sonnet-20240229',
                    ...commonConfig
                });
                break;
            
            case 'google':
                this.llm = new ChatGoogleGenerativeAI({
                    apiKey: config.apiKey,
                    modelName: config.model || 'gemini-pro',
                    ...commonConfig
                });
                break;
            
            case 'microchip':
                // For Microchip API, we'll use a custom implementation
                // This will be handled separately in the server
                break;
            
            default:
                throw new Error(`Unsupported provider: ${config.provider}`);
        }
    }

    private async initializeTools(): Promise<void> {
        // Initialize built-in tools
        this.tools = [
            await this.createCalculatorTool(),
            await this.createWebSearchTool(),
            await this.createCodeAnalysisTool(),
            await this.createMicrochipDocumentationTool()
        ];
    }

    private async createCalculatorTool(): Promise<Tool> {
        return new Tool({
            name: 'calculator',
            description: 'Useful for performing mathematical calculations. Input should be a mathematical expression.',
            func: async (input: string) => {
                try {
                    // Simple calculator implementation
                    const result = eval(input.replace(/[^0-9+\-*/().\s]/g, ''));
                    return `The result of ${input} is ${result}`;
                } catch (error) {
                    return `Error calculating ${input}: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        });
    }

    private async createWebSearchTool(): Promise<Tool> {
        return new Tool({
            name: 'web_search',
            description: 'Search the web for information. Input should be a search query.',
            func: async (query: string) => {
                try {
                    // This would integrate with a web search API
                    // For now, return a placeholder
                    return `Web search results for "${query}": [This would contain actual search results from a web search API]`;
                } catch (error) {
                    return `Error searching for ${query}: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        });
    }

    private async createCodeAnalysisTool(): Promise<Tool> {
        return new Tool({
            name: 'code_analysis',
            description: 'Analyze code snippets for issues, improvements, or explanations. Input should be code.',
            func: async (code: string) => {
                try {
                    // Basic code analysis
                    const lines = code.split('\n').length;
                    const hasComments = code.includes('//') || code.includes('/*');
                    const hasFunctions = code.includes('function') || code.includes('=>');
                    
                    return `Code analysis results:
- Lines of code: ${lines}
- Contains comments: ${hasComments}
- Contains functions: ${hasFunctions}
- Language appears to be: ${this.detectLanguage(code)}`;
                } catch (error) {
                    return `Error analyzing code: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        });
    }

    private async createMicrochipDocumentationTool(): Promise<Tool> {
        return new Tool({
            name: 'microchip_docs',
            description: 'Search Microchip documentation and knowledge base. Input should be a technical query.',
            func: async (query: string) => {
                try {
                    // This would integrate with Microchip's documentation API
                    return `Microchip documentation search for "${query}": [This would contain relevant documentation snippets]`;
                } catch (error) {
                    return `Error searching Microchip docs: ${error instanceof Error ? error.message : 'Unknown error'}`;
                }
            }
        });
    }

    private detectLanguage(code: string): string {
        if (code.includes('#include') || code.includes('int main')) return 'C/C++';
        if (code.includes('function') || code.includes('const ') || code.includes('let ')) return 'JavaScript';
        if (code.includes('def ') || code.includes('import ')) return 'Python';
        if (code.includes('public class') || code.includes('System.out')) return 'Java';
        return 'Unknown';
    }

    private async initializeAgent(): Promise<void> {
        if (!this.llm || this.tools.length === 0) {
            throw new Error('LLM and tools must be initialized before creating agent');
        }

        try {
            // Create a React agent with tools
            const prompt = await pull<ChatPromptTemplate>("hwchase17/react");
            
            this.agent = await createReactAgent({
                llm: this.llm,
                tools: this.tools,
                prompt: prompt,
            });

            this.agent = new AgentExecutor({
                agent: this.agent,
                tools: this.tools,
                verbose: true,
                maxIterations: 5,
                memory: this.config?.enableMemory ? this.memory : undefined
            });
        } catch (error) {
            console.error('Error creating agent:', error);
            // Fallback to simple conversation chain
            this.agent = new ConversationChain({
                llm: this.llm,
                memory: this.memory
            }) as any;
        }
    }

    public async sendMessage(message: string, conversationId?: string): Promise<AgentResponse> {
        if (!this.llm) {
            throw new Error('Agent service not initialized');
        }

        const convId = conversationId || this.currentConversationId;
        
        try {
            let response: string;
            let toolCalls: ToolCall[] = [];

            if (this.agent && this.tools.length > 0) {
                // Use agent with tools
                const result = await this.agent.invoke({
                    input: message,
                    chat_history: this.getConversationHistory(convId)
                });
                response = result.output || result.text || 'No response generated';
                
                // Extract tool calls if available
                if (result.intermediateSteps) {
                    toolCalls = this.extractToolCalls(result.intermediateSteps);
                }
            } else {
                // Use simple LLM call
                const result = await this.llm.invoke([new HumanMessage(message)]);
                response = result.content as string;
            }

            // Store conversation
            this.addToConversation(convId, {
                id: uuidv4(),
                role: 'user',
                content: message,
                timestamp: new Date()
            });

            this.addToConversation(convId, {
                id: uuidv4(),
                role: 'assistant',
                content: response,
                timestamp: new Date(),
                toolCalls: toolCalls.length > 0 ? toolCalls : undefined
            });

            return {
                message: response,
                toolCalls: toolCalls.length > 0 ? toolCalls : undefined,
                conversationId: convId
            };

        } catch (error) {
            console.error('Error processing message:', error);
            throw new Error(`Failed to process message: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private getConversationHistory(conversationId: string): BaseMessage[] {
        const conversation = this.conversations.get(conversationId) || [];
        return conversation.map(msg => 
            msg.role === 'user' 
                ? new HumanMessage(msg.content)
                : new AIMessage(msg.content)
        );
    }

    private addToConversation(conversationId: string, message: ConversationMessage): void {
        if (!this.conversations.has(conversationId)) {
            this.conversations.set(conversationId, []);
        }
        
        const conversation = this.conversations.get(conversationId)!;
        conversation.push(message);
        
        // Keep only last 20 messages to manage memory
        if (conversation.length > 20) {
            this.conversations.set(conversationId, conversation.slice(-20));
        }
    }

    private extractToolCalls(intermediateSteps: any[]): ToolCall[] {
        // Extract tool calls from agent intermediate steps
        return intermediateSteps.map((step, index) => ({
            id: uuidv4(),
            name: step.action?.tool || 'unknown',
            input: step.action?.toolInput || {},
            output: step.observation
        }));
    }

    public getConversation(conversationId: string): ConversationMessage[] {
        return this.conversations.get(conversationId) || [];
    }

    public createNewConversation(): string {
        const newId = uuidv4();
        this.currentConversationId = newId;
        this.conversations.set(newId, []);
        return newId;
    }

    public clearConversation(conversationId?: string): void {
        const convId = conversationId || this.currentConversationId;
        this.conversations.delete(convId);
        if (this.memory) {
            this.memory.clear();
        }
    }

    public getAvailableTools(): string[] {
        return this.tools.map(tool => tool.name);
    }

    public isInitialized(): boolean {
        return this.llm !== null;
    }

    public dispose(): void {
        this.conversations.clear();
        if (this.memory) {
            this.memory.clear();
        }
        this.llm = null;
        this.agent = null;
        this.tools = [];
    }
}
