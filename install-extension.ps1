# PowerShell script to install the Microchip AI Chatbot VS Code extension

Write-Host "Installing Microchip AI Chatbot VS Code Extension..." -ForegroundColor Green

# Check if VS Code is installed
$vscodePath = Get-Command code -ErrorAction SilentlyContinue
if (-not $vscodePath) {
    Write-Host "Error: VS Code 'code' command not found in PATH." -ForegroundColor Red
    Write-Host "Please make sure VS Code is installed and the 'code' command is available." -ForegroundColor Yellow
    Write-Host "You can install the extension manually by:" -ForegroundColor Yellow
    Write-Host "1. Opening VS Code" -ForegroundColor Yellow
    Write-Host "2. Going to Extensions (Ctrl+Shift+X)" -ForegroundColor Yellow
    Write-Host "3. Clicking the '...' menu and selecting 'Install from VSIX...'" -ForegroundColor Yellow
    Write-Host "4. Selecting the microchip-ai-chatbot-1.0.0.vsix file" -ForegroundColor Yellow
    exit 1
}

# Install the extension
$vsixFile = "microchip-ai-chatbot-1.0.0.vsix"
if (Test-Path $vsixFile) {
    Write-Host "Installing extension from $vsixFile..." -ForegroundColor Yellow
    & code --install-extension $vsixFile
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Extension installed successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "The extension will:" -ForegroundColor Cyan
        Write-Host "- Show a robot icon in the VS Code activity bar (left sidebar)" -ForegroundColor White
        Write-Host "- Automatically open the chatbot panel when VS Code starts" -ForegroundColor White
        Write-Host "- Allow you to click the robot icon to access the chatbot anytime" -ForegroundColor White
        Write-Host ""
        Write-Host "Please restart VS Code to see the changes." -ForegroundColor Yellow
    } else {
        Write-Host "Error installing extension. Exit code: $LASTEXITCODE" -ForegroundColor Red
    }
} else {
    Write-Host "Error: $vsixFile not found in current directory." -ForegroundColor Red
    Write-Host "Please make sure you're running this script from the extension directory." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
