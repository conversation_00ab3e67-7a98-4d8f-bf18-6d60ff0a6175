@echo off
echo Installing Microchip AI Chatbot VS Code Extension...
echo.

REM Check if VS Code is installed
where code >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: VS Code 'code' command not found in PATH.
    echo Please make sure VS Code is installed and the 'code' command is available.
    echo.
    echo You can install the extension manually by:
    echo 1. Opening VS Code
    echo 2. Going to Extensions (Ctrl+Shift+X)
    echo 3. Clicking the '...' menu and selecting 'Install from VSIX...'
    echo 4. Selecting the microchip-ai-chatbot-1.0.0.vsix file
    pause
    exit /b 1
)

REM Install the extension
if exist "microchip-ai-chatbot-1.0.0.vsix" (
    echo Installing extension from microchip-ai-chatbot-1.0.0.vsix...
    code --install-extension microchip-ai-chatbot-1.0.0.vsix
    
    if %errorlevel% equ 0 (
        echo.
        echo Extension installed successfully!
        echo.
        echo The extension will:
        echo - Show a robot icon in the VS Code activity bar (left sidebar)
        echo - Automatically open the chatbot panel when VS Code starts
        echo - Allow you to click the robot icon to access the chatbot anytime
        echo.
        echo Please restart VS Code to see the changes.
    ) else (
        echo Error installing extension. Exit code: %errorlevel%
    )
) else (
    echo Error: microchip-ai-chatbot-1.0.0.vsix not found in current directory.
    echo Please make sure you're running this script from the extension directory.
)

echo.
pause
