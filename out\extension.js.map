{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,4BA4CC;AAED,gCAEC;AAnDD,+CAAiC;AACjC,6EAA0E;AAE1E,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;IAE7D,8BAA8B;IAC9B,MAAM,eAAe,GAAG,IAAI,+CAAsB,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;IAElF,gCAAgC;IAChC,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,MAAM,CAAC,MAAM,CAAC,2BAA2B,CACrC,oBAAoB,EACpB,eAAe,EACf;QACI,cAAc,EAAE;YACZ,uBAAuB,EAAE,IAAI;SAChC;KACJ,CACJ,CACJ,CAAC;IAEF,oBAAoB;IACpB,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,6BAA6B,EAAE,GAAG,EAAE;QACxF,eAAe,CAAC,aAAa,EAAE,CAAC;IACpC,CAAC,CAAC,CAAC;IAEH,MAAM,kBAAkB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,gCAAgC,EAAE,GAAG,EAAE;QAC9F,eAAe,CAAC,WAAW,EAAE,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;IAEhE,kDAAkD;IAClD,UAAU,CAAC,GAAG,EAAE;QACZ,eAAe,CAAC,aAAa,EAAE,CAAC;IACpC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,gDAAgD;IAE1D,2CAA2C;IAC3C,MAAM,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;IAC1E,IAAI,CAAC,eAAe,EAAE,CAAC;QACnB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,sFAAsF,EACtF,QAAQ,CACX,CAAC;QACF,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;IACxD,CAAC;AACL,CAAC;AAED,SAAgB,UAAU;IACtB,OAAO,CAAC,GAAG,CAAC,oDAAoD,CAAC,CAAC;AACtE,CAAC"}