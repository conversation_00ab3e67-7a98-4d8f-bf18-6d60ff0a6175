# 🚀 How to Start the Backend Server

The VS Code extension requires a backend server to communicate with the Microchip AI API. Here's how to start it:

## ⚡ Quick Start (Recommended)

### Option 1: Double-click the batch file
1. **Double-click** `start-server.bat` in the project folder
2. A command window will open showing the server status
3. **Keep this window open** while using the extension
4. You can minimize it, but don't close it

### Option 2: Use PowerShell
1. **Right-click** `start-server.ps1` and select "Run with PowerShell"
2. A PowerShell window will open showing the server status
3. **Keep this window open** while using the extension

## 🔧 Manual Start

If the above methods don't work:

1. **Open Command Prompt or PowerShell**
2. **Navigate** to the project folder:
   ```
   cd "C:\Users\<USER>\My Files\Zephyr Support\Clock Support\newAIScreen"
   ```
3. **Run** the server:
   ```
   node server/index.cjs
   ```
4. **Keep the terminal open**

## ✅ How to Know It's Working

When the server starts successfully, you should see:
```
🚀 Microchip AI Proxy Server running on port 3001
📡 Health check: http://localhost:3001/health
🤖 Chat endpoint: http://localhost:3001/api/chat
```

## 🔍 Testing the Server

You can test if the server is running by:
1. **Opening a web browser**
2. **Going to**: http://localhost:3001/health
3. **You should see**: `{"status":"OK","message":"Microchip AI Proxy Server is running"}`

## ❌ Troubleshooting

**Server won't start:**
- Make sure Node.js is installed: `node --version`
- Run `npm install --legacy-peer-deps` first
- Check if port 3001 is already in use

**Extension still shows network error:**
- Make sure the server window is still open
- Check that you see the server startup messages
- Try refreshing the VS Code extension

**Port already in use:**
- Close any other applications using port 3001
- Or kill the existing process and restart

## 🛑 Stopping the Server

To stop the server:
- **Press Ctrl+C** in the server window
- Or simply **close the command/PowerShell window**

## 📝 Important Notes

- **The server must be running** before using the VS Code extension
- **Keep the server window open** while using the extension
- **You only need to start it once** - it will keep running until you stop it
- **The server is safe** - it only acts as a proxy to the Microchip AI API
